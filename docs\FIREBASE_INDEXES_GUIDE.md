# دليل فهارس Firebase Firestore المطلوبة

## نظرة عامة

هذا الدليل يوضح الفهارس المطلوبة في Firebase Firestore لضمان عمل جميع استعلامات نظام الأخبار الذكي بكفاءة.

## الفهارس المطلوبة

### 1. فهرس مجموعة `news`

#### الفهرس المركب للأخبار غير المحللة
```
Collection: news
Fields:
- is_processed (Ascending)
- created_at (Descending)
```

**الغرض:** تسريع استعلامات جلب الأخبار غير المحللة مع الترتيب حسب تاريخ الإنشاء.

#### الفهرس المركب للأخبار الحديثة
```
Collection: news
Fields:
- created_at (Ascending)
- created_at (Descending)
```

**الغرض:** تسريع استعلامات جلب الأخبار الحديثة.

### 2. فهرس مجموعة `notifications`

#### الفهرس المركب لإشعارات المستخدم
```
Collection: notifications
Fields:
- user_id (Ascending)
- created_at (Descending)
```

**الغرض:** تسريع استعلامات جلب إشعارات مستخدم معين مرتبة حسب التاريخ.

## كيفية إنشاء الفهارس

### الطريقة الأولى: عبر وحدة تحكم Firebase

1. اذهب إلى [Firebase Console](https://console.firebase.google.com/)
2. اختر مشروعك
3. اذهب إلى Firestore Database
4. اختر "Indexes" من القائمة الجانبية
5. انقر على "Create Index"
6. أدخل تفاصيل الفهرس كما هو موضح أعلاه

### الطريقة الثانية: عبر Firebase CLI

```bash
# تثبيت Firebase CLI
npm install -g firebase-tools

# تسجيل الدخول
firebase login

# تهيئة المشروع
firebase init firestore

# إضافة الفهارس إلى firestore.indexes.json
```

### الطريقة الثالثة: عبر الرابط المباشر

عندما تظهر رسالة خطأ الفهرس، انقر على الرابط المرفق في رسالة الخطأ لإنشاء الفهرس تلقائياً.

## ملف firestore.indexes.json

```json
{
  "indexes": [
    {
      "collectionGroup": "news",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "is_processed",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "created_at",
          "order": "DESCENDING"
        }
      ]
    },
    {
      "collectionGroup": "notifications",
      "queryScope": "COLLECTION",
      "fields": [
        {
          "fieldPath": "user_id",
          "order": "ASCENDING"
        },
        {
          "fieldPath": "created_at",
          "order": "DESCENDING"
        }
      ]
    }
  ],
  "fieldOverrides": []
}
```

## الحل البديل المطبق

في الإصدار الحالي، تم تطبيق حل بديل يتجنب الحاجة للفهارس المركبة عبر:

1. **استعلام بسيط:** استخدام فلتر واحد فقط (`is_processed == false`)
2. **ترتيب يدوي:** ترتيب النتائج في الكود بدلاً من قاعدة البيانات
3. **تحسين الأداء:** جلب عدد أكبر من النتائج وفلترتها محلياً

## مراقبة الأداء

لمراقبة أداء الاستعلامات:

1. اذهب إلى Firebase Console
2. اختر Firestore Database
3. اختر "Usage" لمراقبة استهلاك القراءات والكتابات
4. راقب أوقات الاستجابة في سجلات التطبيق

## نصائح للتحسين

1. **استخدم الفهارس المركبة** للاستعلامات المعقدة
2. **تجنب الاستعلامات الكبيرة** غير المحدودة
3. **استخدم التخزين المؤقت** للبيانات المتكررة
4. **راقب استهلاك الموارد** بانتظام

## استكشاف الأخطاء

### خطأ "The query requires an index"

**السبب:** الاستعلام يتطلب فهرس مركب غير موجود

**الحل:**
1. انقر على الرابط في رسالة الخطأ لإنشاء الفهرس
2. أو استخدم الحل البديل المطبق في الكود

### بطء في الاستعلامات

**السبب:** عدم وجود فهارس مناسبة

**الحل:**
1. تحقق من وجود الفهارس المطلوبة
2. راجع تعقيد الاستعلامات
3. استخدم التخزين المؤقت عند الإمكان

---

**ملاحظة:** هذا الدليل يتم تحديثه مع إضافة استعلامات جديدة للنظام.
