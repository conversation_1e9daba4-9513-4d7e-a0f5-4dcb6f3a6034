#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام تنظيف الأخبار الفوري
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta
from unittest.mock import Mock, AsyncMock

# إضافة مسار المشروع
project_root = os.path.dirname(os.path.abspath(__file__))
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

from services.automatic_news_notifications import AutomaticNewsNotifications, NotificationType, NotificationPriority
from services.automatic_news_scheduler import AutomaticNewsScheduler

class MockNewsItem:
    """محاكاة عنصر خبر"""
    def __init__(self, news_id, title, content, source="test"):
        self.id = news_id
        self.title = title
        self.content = content
        self.source = source
        self.url = f"https://test.com/{news_id}"
        self.symbols = ["BTC", "ETH"]
        self.sentiment = "positive"
        self.ai_analysis = "تحليل تجريبي"
        self.published_at = datetime.now()

class MockDB:
    """محاكاة قاعدة البيانات"""
    def __init__(self):
        self.collections = {
            'news': {},
            'user_settings': {},
            'notifications': {},
            'notification_preferences': {}
        }
    
    def collection(self, name):
        if name not in self.collections:
            self.collections[name] = {}
        return MockCollection(self.collections[name])

class MockCollection:
    """محاكاة مجموعة في قاعدة البيانات"""
    def __init__(self, data):
        self.data = data
    
    def document(self, doc_id):
        return MockDocument(self.data, doc_id)
    
    def where(self, field, operator, value):
        return MockQuery(self.data, field, operator, value)

class MockDocument:
    """محاكاة وثيقة في قاعدة البيانات"""
    def __init__(self, collection_data, doc_id):
        self.collection_data = collection_data
        self.doc_id = doc_id
    
    def get(self):
        return MockDocumentSnapshot(self.collection_data.get(self.doc_id))
    
    def set(self, data, merge=False):
        if merge and self.doc_id in self.collection_data:
            self.collection_data[self.doc_id].update(data)
        else:
            self.collection_data[self.doc_id] = data
    
    def update(self, data):
        if self.doc_id in self.collection_data:
            self.collection_data[self.doc_id].update(data)
    
    def delete(self):
        if self.doc_id in self.collection_data:
            del self.collection_data[self.doc_id]
            print(f"🗑️ تم حذف الوثيقة {self.doc_id}")

class MockDocumentSnapshot:
    """محاكاة لقطة وثيقة"""
    def __init__(self, data):
        self.data = data
    
    @property
    def exists(self):
        return self.data is not None
    
    def to_dict(self):
        return self.data or {}

class MockQuery:
    """محاكاة استعلام"""
    def __init__(self, data, field, operator, value):
        self.data = data
        self.field = field
        self.operator = operator
        self.value = value
    
    def get(self):
        # محاكاة بسيطة للاستعلام
        results = []
        for doc_id, doc_data in self.data.items():
            if self.field in doc_data:
                if self.operator == '==' and doc_data[self.field] == self.value:
                    results.append(MockDocumentSnapshot(doc_data))
                elif self.operator == '<' and doc_data[self.field] < self.value:
                    results.append(MockDocumentSnapshot(doc_data))
        return results

class MockBot:
    """محاكاة بوت تليجرام"""
    def __init__(self):
        self.sent_messages = []
    
    async def send_message(self, chat_id, text, **kwargs):
        self.sent_messages.append({
            'chat_id': chat_id,
            'text': text,
            'kwargs': kwargs
        })
        print(f"📤 إرسال رسالة للمستخدم {chat_id}: {text[:50]}...")
        return True

async def test_news_cleanup_system():
    """اختبار نظام تنظيف الأخبار"""
    print("🧪 بدء اختبار نظام تنظيف الأخبار الفوري...")
    
    # إعداد المحاكيات
    mock_db = MockDB()
    mock_bot = MockBot()
    
    # إعداد بيانات المستخدمين التجريبية
    mock_db.collections['user_settings']['user1'] = {
        'lang': 'ar',
        'notifications': True
    }
    mock_db.collections['user_settings']['user2'] = {
        'lang': 'en', 
        'notifications': True
    }
    
    # إنشاء نظام الإشعارات
    notifications_system = AutomaticNewsNotifications(db=mock_db, bot=mock_bot)
    
    # إنشاء أخبار تجريبية
    test_news = [
        MockNewsItem("news_1", "🚨 Bitcoin reaches new high", "Bitcoin price surged to $100,000"),
        MockNewsItem("news_2", "🆕 New altcoin launched", "A new cryptocurrency has been launched"),
    ]
    
    print(f"📰 إنشاء {len(test_news)} خبر تجريبي...")
    
    # حفظ الأخبار في قاعدة البيانات المحاكية
    for news in test_news:
        mock_db.collections['news'][news.id] = {
            'id': news.id,
            'title': news.title,
            'content': news.content,
            'source': news.source,
            'created_at': datetime.now().isoformat(),
            'is_processed': True,
            'interested_users': [],
            'sent_to_users': [],
            'total_interested_users': 0,
            'is_sent_to_all': False
        }
    
    print("💾 تم حفظ الأخبار في قاعدة البيانات المحاكية")
    
    # معالجة الأخبار للإشعارات
    print("📢 معالجة الأخبار للإشعارات...")
    await notifications_system.process_news_for_notifications(test_news)
    
    # التحقق من النتائج
    print("\n📊 نتائج الاختبار:")
    print(f"✉️ عدد الرسائل المرسلة: {len(mock_bot.sent_messages)}")
    
    # عرض الرسائل المرسلة
    for i, message in enumerate(mock_bot.sent_messages, 1):
        print(f"  {i}. للمستخدم {message['chat_id']}: {message['text'][:100]}...")
    
    # التحقق من حذف الأخبار
    remaining_news = len(mock_db.collections['news'])
    print(f"🗑️ الأخبار المتبقية في قاعدة البيانات: {remaining_news}")
    
    if remaining_news == 0:
        print("✅ تم حذف جميع الأخبار بنجاح بعد الإرسال!")
    else:
        print("⚠️ لا تزال هناك أخبار في قاعدة البيانات:")
        for news_id, news_data in mock_db.collections['news'].items():
            print(f"  - {news_id}: {news_data.get('title', 'بدون عنوان')}")
    
    # إحصائيات الإشعارات
    stats = await notifications_system.get_notification_stats()
    print(f"\n📈 إحصائيات الإشعارات:")
    print(f"  - إجمالي المرسل: {stats['total_sent']}")
    print(f"  - إجمالي الفاشل: {stats['total_failed']}")
    print(f"  - معدل النجاح: {stats['success_rate']:.1f}%")
    
    print("\n✅ انتهى الاختبار!")

if __name__ == "__main__":
    asyncio.run(test_news_cleanup_system())
